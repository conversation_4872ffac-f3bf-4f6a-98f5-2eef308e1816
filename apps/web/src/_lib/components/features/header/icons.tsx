import { v4 as uuidv4 } from "uuid";
import { twMerge } from "tailwind-merge";
import {
  Home,
  BookOpen,
  BriefcaseBusiness,
  MonitorPlay,
  UserRound,
  type LucideIcon,
} from "lucide-react";
import { Button } from "@web/_lib/components/ui";
import { type NavigationLinksType } from "@web/_lib/models/links";

// icon maping
export const IconMap = new Map<string, LucideIcon>([
  ["Home", Home],
  ["BookOpen", BookOpen],
  ["BriefcaseBusiness", BriefcaseBusiness],
  ["MonitorPlay", MonitorPlay],
  ["UserRound", UserRound],
]);

// define icon component
import { v4 as uuidv4 } from "uuid";
import { twMerge } from "tailwind-merge";
import {
  Home,
  BookOpen,
  BriefcaseBusiness,
  MonitorPlay,
  UserRound,
  type LucideIcon,
} from "lucide-react";
import { Button } from "@web/_lib/components/ui";
import { type NavigationLinksType } from "@web/_lib/models/links";

// icon maping
export const IconMap = new Map<string, LucideIcon>([
  ["Home", Home],
  ["BookOpen", BookOpen],
  ["BriefcaseBusiness", BriefcaseBusiness],
  ["MonitorPlay", MonitorPlay],
  ["UserRound", UserRound],
]);