"use client";
import React, { useMemo, useCallback } from "react";
import Link from "next/link";
import { NavigationLinks } from "@web/_lib/models/links";

export function AnimatedList({
  open,
  toggleMenu,
}: {
  open: boolean;
  toggleMenu: () => void;
}): React.ReactElement {
  // Memoize navigation data to avoid unnecessary re-renders
  const data = useMemo(
    () => (Array.isArray(NavigationLinks) ? NavigationLinks : []),
    [],
  );

  // get animation styles
  const getStyles = useCallback((isOpen: boolean): React.CSSProperties => {
    return {
      opacity: isOpen ? 1 : 0,
      transform: isOpen ? "translateY(0px)" : "translateY(20px)",
    };
  }, []);

  return (
    <div
      id="mobile-menu-list"
      className="absolute top-35 left-0 flex w-full flex-col items-center gap-4 md:hidden"
      style={{
        zIndex: open ? 10 : -10,
        visibility: open ? "visible" : "hidden",
      }}
    >
      {data.map((link, index) => {
        if (!link.show) return undefined;
        return (
          <Link
            key={link.id}
            href={link.url ?? "#"}
            onClick={toggleMenu}
            className="text-dark-text cursor-pointer p-2 text-3xl uppercase"
            style={{
              transition: "all 0.3s ease-out",
              transitionDelay: `${index * 70}ms`,
              ...getStyles(open),
            }}
          >
            {link.name}
          </Link>
        );
      })}
    </div>
  );
}
