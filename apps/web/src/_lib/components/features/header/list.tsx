import { v4 as uuidv4 } from "uuid";
import { twMerge } from "tailwind-merge";
import {
  Home,
  BookOpen,
  BriefcaseBusiness,
  MonitorPlay,
  UserRound,
  type LucideIcon,
} from "lucide-react";
import { Button } from "@web/_lib/components/ui";
import { type NavigationLinksType } from "@web/_lib/models/links";

// icon maping
export const IconMap = new Map<string, LucideIcon>([
  ["Home", Home],
  ["BookOpen", BookOpen],
  ["BriefcaseBusiness", BriefcaseBusiness],
  ["MonitorPlay", MonitorPlay],
  ["UserRound", UserRound],
]);

// local component
export function List({
  link,
  className,
  style,
}: {
  link: NavigationLinksType;
  className?: string;
  style?: React.CSSProperties;
}): React.ReactElement {
  const Icon = IconMap.get(link.icon);
  return (
    <Button
      key={link.id}
      href={link.url ?? "#"}
      variant="outline"
      size="sm"
      className={twMerge("border-none", className)}
      style={style}
    >
      <Icon className="size-4" />
      {link.name}
    </Button>
  );
}
